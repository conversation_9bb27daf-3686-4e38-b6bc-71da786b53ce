import React from "react";
import { View, Text, StyleSheet } from "react-native";
import PropTypes from "prop-types";
import ListView from 'deprecated-react-native-listview'
import { isIos } from "../../../../imilab-rn-sdk/utils/Utils";
/**
 @example
 <StringSpinner
    data={["1",'2','3','4','5','6','7','8','9','10','11','12']}
    selectedIndex={6}
    unit={"月"}
    onValueChange={(value, selectedIndex) => {
                        console.log("StringSpinner组件----",value,selectedIndex);
                    }}
    optionsWrapperWidth={100}
    optionHeight={60}
    highlightBorderColor={"transparent"}
    highlightBorderWidth={1}
    activeItemColor={"#222121"}
    itemColor={"#B4B4B4"}
 />
 * */
/**
 * @description 滚动列表选择器
 * @property {array<string>} data - 选择列表的数据源
 * @property {number} selectedIndex - 当前选中的下标
 * @property {string} unit - 单位，如年，月，日等
 * @property {function} onValueChange - 选择项变化的回调
 * @property {number} optionsWrapperWidth - 控件的宽度
 * @property {number} optionHeight - 每一个选项的高度
 * @property {string} highlightBorderColor - 被选中项上下两根边框线的颜色
 * @property {number} highlightBorderWidth - 被选中项上下两根边框线的粗细
 * @property {string} activeItemColor - 被选中项的字体颜色
 * @property {string} itemColor - 常态项的字体颜色
 * @property {number} activeItemFontSize - 被选中项的字体大小
 * @property {number} itemFontSize - 常态项的字体大小
 */


export default class StringSpinner extends React.Component {
    constructor(props) {
        super(props);
        this.ds = new ListView.DataSource({ rowHasChanged: () => true });
        this.timer = null;
        this.momentumStart = false;
        this.dragStart = false;
        this.data = this.getData(props.data);
        this.state = {
            dataSource: this.ds.cloneWithRows(this.data),
            selectedIndex: props.data[props.selectedIndex]
                ? props.selectedIndex + 1
                : 0
        };
    }

    componentDidMount() {
        if (this.state.selectedIndex === undefined) {
            return;
        }

        setTimeout(() => {
            
            this.scrollToIndex(this.state.selectedIndex-1);
        }, 0);
    }

    componentWillUnmount() {
        if (this.timer) {
            clearTimeout(this.timer);
        }
    }

    UNSAFE_componentWillReceiveProps(nextProps) {
        if(this.state.selectedIndex !== nextProps.selectedIndex){
            this.setState({
                dataSource: this.ds.cloneWithRows(this.data),
                selectedIndex: nextProps.data[nextProps.selectedIndex]
                    ? nextProps.selectedIndex + 1
                    : 0
            })
            setTimeout(() => {
                this.scrollToIndex(nextProps.selectedIndex);
            }, 0);
        }
        if (this.props.data.length !== nextProps.data.length) {
            this.data = this.getData(nextProps.data);

            if (!this.data[this.state.selectedIndex]) {
                this.setState({
                    selectedIndex: nextProps.data.length
                });
            }

            this.setState({
                dataSource: this.ds.cloneWithRows(this.data)
            });
        }
    }

    getData = optionsData => {
        const { optionsCount } = this.props;

        return this.createEmptyArray(optionsCount / 2).concat(
            optionsData.concat(this.createEmptyArray(optionsCount / 2))
        );
    };

    createEmptyArray = arrayLength => {
        return new Array(Math.floor(arrayLength)).fill(" ");
    };

    onMomentumScrollEnd = event => {
        const el = {
            nativeEvent: {
                contentOffset: {
                    y: event.nativeEvent.contentOffset.y
                }
            }
        };

        if (this.timer) {
            clearTimeout(this.timer);
        }

        // ios需要这个，否则会不停传递，bug1133
        if (isIos()) {
            if (!this.dragStart && this.momentumStart) {
                this.selectOption(el);
            }
        } else {
            this.selectOption(el);
        }

        this.momentumStart = false;
    };

    onMomentumScrollBegin = () => {
        this.momentumStart = true;

        if (this.timer) {
            clearTimeout(this.timer);
        }
    };

    onScrollBeginDrag = () => {
        this.dragStart = true;

        if (this.timer) {
            clearTimeout(this.timer);
        }
    };

    onScrollEndDrag = event => {
        const el = {
            nativeEvent: {
                contentOffset: {
                    y: event.nativeEvent.contentOffset.y
                }
            }
        };

        this.dragStart = false;

        if (this.timer) {
            clearTimeout(this.timer);
        }

        this.timer = setTimeout(() => {
            if (!this.dragStart && !this.momentumStart) {
                this.selectOption(el);
            }
        }, 10);
    };

    selectOption = event => {
        const { optionsCount, optionHeight } = this.props;
        const y = event.nativeEvent.contentOffset
            ? event.nativeEvent.contentOffset.y
            : 0;
        const selectedIndex = Math.round(y / optionHeight);
        const selectedIndexWithEmptyBlock =
            selectedIndex + Math.floor(optionsCount / 2);

        this.setState({
            selectedIndex: selectedIndexWithEmptyBlock,
            dataSource: this.ds.cloneWithRows(this.data)
        });
        this.scrollToIndex(selectedIndex);

        if (this.props.onValueChange) {
            this.props.onValueChange(
                this.data[selectedIndexWithEmptyBlock],
                selectedIndex
            );
        }
    };

    scrollToIndex(index) {
        const { optionHeight } = this.props;

        this.listRef?.scrollTo({ y: index * optionHeight });
    }

    render() {
        const {
            optionsCount,
            optionsWrapperWidth,
            optionHeight,
            highlightBorderWidth,
            highlightBorderColor,
            itemColor,
            activeItemColor,
            wrapperBackground,
            activeItemFontSize,
            itemFontSize
        } = this.props;

        const highlightVarStyle = {
            top: Math.floor(optionsCount * 0.5) * optionHeight,
            height: optionHeight,
            borderTopWidth: highlightBorderWidth,
            borderBottomWidth: highlightBorderWidth,
            borderTopColor: highlightBorderColor,
            borderBottomColor: highlightBorderColor,
           // backgroundColor:"#678"
        };

        return (
            <View
                style={[
                    {
                        height: optionHeight * optionsCount,
                        width: optionsWrapperWidth,
                        backgroundColor: wrapperBackground
                    },
                    styles.container
                ]}
            >
                <View style={[highlightVarStyle, styles.highlight]} />
                <ListView
                    enableEmptySections
                    dataSource={this.state.dataSource}
                    showsVerticalScrollIndicator={false}
                    onMomentumScrollEnd={this.onMomentumScrollEnd}
                    onMomentumScrollBegin={this.onMomentumScrollBegin}
                    onScrollEndDrag={this.onScrollEndDrag}
                    onScrollBeginDrag={this.onScrollBeginDrag}
                    alwaysBounceVertical={false}
                    bounces={false}
                    initialListSize={this.data.length}
                    ref={ref => {
                        this.listRef = ref;
                    }}
                    renderRow={(data, _, id) => (
                        <View style={[{ height: optionHeight,flexDirection:"row" }, styles.itemWrapper]}>
                            <Text
                            style={[
                                { color: itemColor,fontSize:itemFontSize != undefined ? itemFontSize :16 },
                                id == this.state.selectedIndex && { color: activeItemColor,
                                    fontSize:activeItemFontSize != undefined ?activeItemFontSize :23 }
                            ]}
                        >
                           1 {data}
                        </Text>
                            {id == this.state.selectedIndex?<Text fontWeight={"bold"}
                                style={{marginLeft:10,color:"#4A70A5",fontSize:10}}
                            >
                                {this.props.unit}
                            </Text>:null}
                        </View>
                    )}
                />
            </View>
        );
    }
}

StringSpinner.propTypes = {
    optionsCount: PropTypes.number,
    optionsWrapperWidth: PropTypes.number,
    optionHeight: PropTypes.number,
    data: PropTypes.array,
    selectedIndex: PropTypes.number,
    unit:PropTypes.string,
    highlightBorderWidth: PropTypes.number,
    highlightBorderColor: PropTypes.string,
    activeItemColor: PropTypes.string,
    itemColor: PropTypes.string,
    wrapperBackground: PropTypes.string
};

StringSpinner.defaultProps = {
    optionsCount: 3,
    optionsWrapperWidth: 100,
    optionHeight: 60,
    data: [],
    selectedIndex: 0,
    unit:"",
    highlightBorderWidth: 1,
    highlightBorderColor: "#333",
    activeItemColor: "#222121",
    itemColor: "#B4B4B4",
    wrapperBackground: "#fff"
};

const styles = StyleSheet.create({
    container: {
        backgroundColor: "#FFFFFF",
        justifyContent: "center"
    },
    itemWrapper: {
        alignItems: "center",
        justifyContent: "center"
    },
    highlight: {
        width: "100%",
        position: "absolute"
    }
});
